from src.models.user import db
from datetime import datetime

class VulnerabilityD<PERSON>ier(db.Model):
    __tablename__ = "vulnerability_dossiers"

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    candidate_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>("candidates.id"), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    issue_summary = db.Column(db.Text)  # Resumo do caso (polêmica, escândalo, etc.)
    status = db.Column(db.String(100))  # Ex: 'arquivado', 'em andamento', 'resolvido'
    official_defense = db.Column(db.Text)  # Defesa/explicação oficial do candidato
    controversial_statements = db.Column(db.Text)  # JSONB seria melhor para array de objetos
    weak_points_management = db.Column(db.Text)  # Pontos fracos na gestão ou trajetória

    candidate = db.relationship("Candidate", backref=db.backref("vulnerability_dossiers", lazy=True))

    def to_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result

    @classmethod
    def from_dict(cls, data):
        data.pop("id", None)
        data.pop("created_at", None)
        data.pop("updated_at", None)
        return cls(**data)

    def update_from_dict(self, data):
        data.pop("id", None)
        data.pop("created_at", None)
        data.pop("updated_at", None)

        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

        self.updated_at = datetime.utcnow()

    def __repr__(self):
        return f"<VulnerabilityDossier {self.issue_summary[:50]}>"


