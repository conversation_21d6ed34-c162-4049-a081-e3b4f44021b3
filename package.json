{"name": "kairos", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npx prisma db push && npx prisma generate && next build", "start": "next start", "lint": "next lint", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json,css}\"", "postinstall": "prisma generate", "seed": "ts-node --require tsconfig-paths/register prisma/seed.ts", "vercel-build": "prisma generate && prisma migrate deploy && next build", "scan": "next dev & npx react-scan@latest localhost:3000"}, "dependencies": {"@auth/core": "^0.37.4", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@hookform/resolvers": "^3.9.1", "@mdxeditor/editor": "^3.20.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.0.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.20.6", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto": "^1.0.1", "embla-carousel-react": "^8.5.1", "ioredis": "^5.4.2", "lucide-react": "^0.468.0", "next": "^15.1.5", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "prisma": "^6.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "react-icons": "^5.4.0", "react-markdown": "^9.0.1", "react-scan": "^0.2.14", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}, "lint-staged": {"*.js": "eslint --cache --fix"}}