generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider          = "mysql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model User {
  id                  Int          @id @default(autoincrement())
  name                String
  email               String       @unique
  password            String
  role                Role         @default(USER)
  sessionVersion      Int          @default(1)
  avatarGroupId       String
  voiceId             String?
  difyAgent           String?
  difyContentCreation String?
  briefings           Briefing[]
  news                News[]
  suggestions         Suggestion[]
  videos              Video[]
  socialTokens        UserSocialToken[]
  instagramDailySnapshots InstagramDailySnapshot[]
  instagramMediaSnapshots InstagramMediaSnapshot[]
  instagramDemographics InstagramDemographics[]
  instagramInsightsCache InstagramInsightsCache[]
  @@index([role])
}

model Suggestion {
  id               Int        @id @default(autoincrement())
  post_url         String
  user_photo       String?
  post_image       String?    // Optional since Twitter posts might not have images
  name_profile     String     // The @ username
  post_text        String     @db.Text
  socialmedia_name String     // "instagram" or "x"
  date             DateTime   @default(now())
  userId           Int
  status           Status     @default(EM_ANALISE)
  briefings        Briefing[]
  user             User       @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Briefing {
  id           Int         @id @default(autoincrement())
  suggestionId Int?
  title        String      @db.VarChar(500)
  text         String?     @db.VarChar(5000)
  date         DateTime
  status       Status
  difyStatus   DifyStatus  @default(EM_PRODUCAO)
  difyMessage  String?     @db.VarChar(500)
  userId       Int
  newsId       Int?
  sources      String?     @db.VarChar(5000)
  news         News?       @relation(fields: [newsId], references: [id])
  suggestion   Suggestion? @relation(fields: [suggestionId], references: [id])
  user         User        @relation(fields: [userId], references: [id])

  @@index([suggestionId], map: "Briefing_suggestionId_fkey")
  @@index([newsId], map: "Briefing_newsId_fkey")
  @@index([userId], map: "Briefing_userId_fkey")
}

model News {
  id        Int        @id @default(autoincrement())
  title     String     @db.VarChar(500)
  summary   String     @db.VarChar(1000)
  userId    Int
  url       String     @db.VarChar(500)
  thumbnail String     @db.VarChar(500)
  status    Status
  text      String     @db.VarChar(5000)
  date      DateTime   @default(now())
  Briefing  Briefing[]
  user      User       @relation(fields: [userId], references: [id])

  @@index([userId], map: "News_userId_fkey")
}

model Video {
  id             Int          @id @default(autoincrement())
  userId         Int
  title          String       @db.VarChar(500)
  legenda        String?       @db.VarChar(2000)
  url            String?      @db.VarChar(500)
  heygenErrorMsg String?
  heygenStatus   HeyGenStatus @default(PROCESSING)
  heygenVideoId  String?      @unique
  height         Int
  width          Int
  creationDate   DateTime     @default(now())
  transcription  String       @default("") @db.VarChar(2000)
  user           User         @relation(fields: [userId], references: [id])

  @@index([userId], map: "Video_userId_fkey")  
}

model ApiLog {
  id           Int      @id @default(autoincrement())
  route        String   @db.VarChar(500)
  body         Json?
  error        String?  @db.VarChar(1000)
  responseCode Int?
  time         DateTime @default(now())
}

enum Role {
  USER
  ADMIN
}

enum Status {
  EM_ANALISE
  EM_PRODUCAO
  PRODUZIDO
  APROVADO
  ARQUIVADO
}

enum HeyGenStatus {
  PROCESSING
 
 SUCCESS
  FAILED
}

enum DifyStatus {
  EM_PRODUCAO
  PRONTO
  ERROR
}

// Instagram Integration Models

model UserSocialToken {
  id                  Int      @id @default(autoincrement())
  userId              Int
  platform            SocialPlatform
  accessToken         String   @db.Text // Encrypted token
  instagramAccountId  String?  @db.VarChar(255) // Instagram Business Account ID
  expiresAt           DateTime?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, platform])
  @@index([userId])
}

model InstagramDailySnapshot {
  id                  Int      @id @default(autoincrement())
  userId              Int
  instagramAccountId  String   @db.VarChar(255)
  snapshotDate        DateTime @db.Date
  snapshotTimestamp   DateTime
  reach               Int      @default(0)
  followerChange      Int      @default(0)
  profileViews        Int      @default(0)
  accountsEngaged     Int      @default(0)
  createdAt           DateTime @default(now())
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, instagramAccountId, snapshotDate])
  @@index([userId])
  @@index([snapshotDate])
}

model InstagramMediaSnapshot {
  id                  Int      @id @default(autoincrement())
  userId              Int
  mediaId             String   @db.VarChar(255)
  snapshotTimestamp   DateTime
  totalInteractions   Int      @default(0)
  reach               Int      @default(0)
  saved               Int      @default(0)
  likes               Int      @default(0)
  comments            Int      @default(0)
  shares              Int      @default(0)
  views               Int      @default(0)
  createdAt           DateTime @default(now())
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([mediaId])
  @@index([snapshotTimestamp])
}

model InstagramDemographics {
  id                  Int      @id @default(autoincrement())
  userId              Int
  instagramAccountId  String   @db.VarChar(255)
  genderAgeData       Json     // Store F_25-34, M_35-44 format
  citiesData          Json     // Store city distribution
  lastUpdated         DateTime
  createdAt           DateTime @default(now())
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, instagramAccountId])
  @@index([userId])
}

model InstagramInsightsCache {
  id          Int      @id @default(autoincrement())
  userId      Int
  metricType  InstagramMetricType
  data        Json
  period      String?  @db.VarChar(20)
  cachedAt    DateTime @default(now())
  expiresAt   DateTime
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
}

enum SocialPlatform {
  INSTAGRAM
  TIKTOK
  TWITTER
}

enum InstagramMetricType {
  ACCOUNT
  MEDIA
  AUDIENCE
}
