import { NextRequest, NextResponse } from 'next/server';
import { InstagramTokenService } from '@/services/InstagramTokenService';

export async function POST(request: NextRequest) {
    try {
        const { userId } = await request.json();

        if (!userId) {
            return NextResponse.json(
                { error: 'User ID is required' },
                { status: 400 }
            );
        }

        const hasToken = await InstagramTokenService.hasToken(userId);

        if (!hasToken) {
            return NextResponse.json(
                { error: 'No Instagram token found' },
                { status: 404 }
            );
        }

        return NextResponse.json({
            hasToken: true,
            message: 'Instagram token found'
        });

    } catch (error) {
        console.error('Error checking Instagram token:', error);
        
        return NextResponse.json(
            { 
                error: 'Failed to check Instagram token',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
