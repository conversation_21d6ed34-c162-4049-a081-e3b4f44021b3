<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Formulário para Análise Estratégica Política</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        background-color: hsl(220, 25%, 10%);
        color: hsl(210, 20%, 98%);
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(to right, hsl(191, 65%, 53%), #0085A3);
        color: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.9;
      }

      .form-container {
        background: hsl(220, 30%, 13%);
        border: 1px solid hsl(220, 15%, 20%);
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        overflow: hidden;
      }

      .form-header {
        padding: 20px;
        background: hsl(220, 15%, 20%);
        border-bottom: 1px solid hsl(220, 15%, 20%);
        text-align: center;
      }

      .form-header h2 {
        color: hsl(210, 20%, 98%);
        font-size: 1.8rem;
        margin-bottom: 10px;
      }

      .form-header p {
        color: hsl(220, 10%, 70%);
        font-size: 1rem;
      }

      .tabs {
        display: flex;
        background: hsl(220, 15%, 20%);
        border-bottom: 1px solid hsl(220, 15%, 20%);
        overflow-x: auto;
        padding: 4px;
        gap: 8px;
      }

      .tab {
        flex: 1;
        min-width: 120px;
        padding: 12px 16px;
        border: none;
        background: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
        text-decoration: none;
        color: hsl(210, 20%, 98%);
        border-radius: 6px;
        opacity: 0.7;
      }

      .tab:hover {
        background: hsl(220, 15%, 25%);
        opacity: 0.9;
      }

      .tab.active {
        background: linear-gradient(to right, hsl(191, 65%, 53%), #0085A3);
        color: white;
        opacity: 1;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 133, 163, 0.3);
      }

      .tab-icon {
        font-size: 1.2rem;
        margin-bottom: 4px;
      }

      .tab-label {
        font-weight: inherit;
        font-size: 0.85rem;
      }

      .form-content {
        padding: 30px;
      }

      .form-section h3 {
        color: hsl(210, 20%, 98%);
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 2px solid hsl(191, 100%, 50%);
        font-size: 1.3rem;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      .form-group label {
        margin-bottom: 5px;
        font-weight: 600;
        color: hsl(210, 20%, 98%);
        font-size: 0.9rem;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        padding: 12px;
        border: 1px solid hsl(220, 15%, 20%);
        border-radius: 6px;
        font-size: 1rem;
        transition: all 0.3s ease;
        font-family: inherit;
        background-color: hsl(220, 30%, 13%);
        color: hsl(210, 20%, 98%);
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: hsl(191, 100%, 50%);
        box-shadow: 0 0 0 2px hsl(191, 100%, 50%, 0.2);
      }

      .form-group input::placeholder,
      .form-group textarea::placeholder {
        color: hsl(220, 10%, 50%);
      }

      .form-group select option {
        background-color: hsl(220, 30%, 13%);
        color: hsl(210, 20%, 98%);
      }

      .form-group textarea {
        resize: vertical;
        min-height: 80px;
      }

      .placeholder-content {
        text-align: center;
        padding: 40px;
        color: hsl(220, 10%, 70%);
      }

      .placeholder-content h3 {
        color: hsl(210, 20%, 98%);
        margin-bottom: 20px;
      }

      .placeholder-content ul {
        text-align: left;
        max-width: 400px;
        margin: 20px auto;
      }

      .placeholder-content li {
        margin-bottom: 8px;
      }

      .form-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid hsl(220, 15%, 20%);
      }

      .btn {
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .btn-primary {
        background: linear-gradient(to right, hsl(191, 65%, 53%), #0085A3);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 133, 163, 0.3);
      }

      .btn-primary:hover {
        background: linear-gradient(to right, hsl(191, 75%, 58%), hsl(191, 100%, 38%));
        box-shadow: 0 0 15px rgba(0, 133, 163, 0.3);
        transform: translateY(-1px);
      }

      .btn-secondary {
        background: hsl(220, 15%, 25%);
        color: hsl(210, 20%, 98%);
        border: 1px solid hsl(220, 15%, 20%);
      }

      .btn-secondary:hover {
        background: hsl(220, 15%, 30%);
      }

      .hidden {
        display: none;
      }

      /* Custom scrollbar for dark theme */
      ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      ::-webkit-scrollbar-track {
        background: hsl(220, 25%, 10%);
      }

      ::-webkit-scrollbar-thumb {
        background: hsl(220, 15%, 20%);
        border-radius: 5px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: hsl(191, 100%, 50%);
      }

      @media (max-width: 768px) {
        .form-grid {
          grid-template-columns: 1fr;
        }

        .tabs {
          flex-wrap: wrap;
          gap: 4px;
        }

        .tab {
          min-width: 100px;
          font-size: 0.8rem;
          padding: 10px 12px;
        }

        .tab-label {
          font-size: 0.75rem;
        }

        .form-actions {
          flex-direction: column;
        }

        .header h1 {
          font-size: 2rem;
        }

        .header p {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Formulário para Análise Estratégica Política</h1>
        <p>Coleta de Dados para Análise Estratégica</p>
      </div>

      <div class="form-container">
        <div class="form-header">
          <h2>Formulário de Análise Política</h2>
          <p>Preencha as informações do candidato para análise estratégica</p>
        </div>

        <div class="tabs">
          <a href="#candidato" class="tab active" onclick="showTab('candidato', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </span>
            <span class="tab-label">Candidato</span>
          </a>
          <a href="#comunicacao" class="tab" onclick="showTab('comunicacao', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M7 8h10"></path>
                <path d="M7 12h4"></path>
                <path d="M10 16v-4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v4"></path>
                <path d="M9 16v-4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v4"></path>
              </svg>
            </span>
            <span class="tab-label">Comunicação</span>
          </a>
          <a href="#trajetoria" class="tab" onclick="showTab('trajetoria', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="22,6 12,16 2,6"></polyline>
                <path d="M16 16v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2v-4"></path>
                <path d="M22 16v4a2 2 0 0 1-2 2h-4"></path>
                <path d="M2 16v4a2 2 0 0 0 2 2h4"></path>
              </svg>
            </span>
            <span class="tab-label">Trajetória</span>
          </a>
          <a href="#plataforma" class="tab" onclick="showTab('plataforma', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </span>
            <span class="tab-label">Plataforma</span>
          </a>
          <a href="#eleitorado" class="tab" onclick="showTab('eleitorado', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </span>
            <span class="tab-label">Eleitorado</span>
          </a>
          <a href="#cenario" class="tab" onclick="showTab('cenario', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon>
                <line x1="8" y1="2" x2="8" y2="18"></line>
                <line x1="16" y1="6" x2="16" y2="22"></line>
              </svg>
            </span>
            <span class="tab-label">Cenário</span>
          </a>
          <a href="#vulnerabilidades" class="tab" onclick="showTab('vulnerabilidades', this)">
            <span class="tab-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </span>
            <span class="tab-label">Vulnerabilidades</span>
          </a>
        </div>

        <form class="form-content">
          <!-- Candidato Tab -->
          <div id="candidato" class="tab-content">
            <div class="form-section">
              <h3>Dados Pessoais Básicos</h3>
              <div class="form-grid">
                <div class="form-group">
                  <label for="fullName">Nome Completo *</label>
                  <input type="text" id="fullName" name="fullName" required>
                </div>

                <div class="form-group">
                  <label for="urnName">Nome de Urna</label>
                  <input type="text" id="urnName" name="urnName">
                </div>

                <div class="form-group">
                  <label for="age">Idade</label>
                  <input type="number" id="age" name="age">
                </div>

                <div class="form-group">
                  <label for="birthPlace">Local de Nascimento</label>
                  <input type="text" id="birthPlace" name="birthPlace">
                </div>

                <div class="form-group">
                  <label for="maritalStatus">Estado Civil</label>
                  <select id="maritalStatus" name="maritalStatus">
                    <option value="">Selecione...</option>
                    <option value="solteiro">Solteiro(a)</option>
                    <option value="casado">Casado(a)</option>
                    <option value="divorciado">Divorciado(a)</option>
                    <option value="viuvo">Viúvo(a)</option>
                    <option value="uniao_estavel">União Estável</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="religion">Religião</label>
                  <input type="text" id="religion" name="religion">
                </div>
              </div>

              <h3>Formação e Profissão</h3>
              <div class="form-grid">
                <div class="form-group full-width">
                  <label for="education">Formação Acadêmica</label>
                  <textarea id="education" name="education" rows="3"></textarea>
                </div>

                <div class="form-group">
                  <label for="currentProfession">Profissão Atual</label>
                  <input type="text" id="currentProfession" name="currentProfession">
                </div>

                <div class="form-group">
                  <label for="previousProfession">Profissão Anterior</label>
                  <input type="text" id="previousProfession" name="previousProfession">
                </div>
              </div>

              <h3>Interesses Pessoais</h3>
              <div class="form-grid">
                <div class="form-group">
                  <label for="hobbies">Hobbies e Interesses</label>
                  <textarea id="hobbies" name="hobbies" rows="3"></textarea>
                </div>

                <div class="form-group">
                  <label for="favoriteTeam">Time de Futebol</label>
                  <input type="text" id="favoriteTeam" name="favoriteTeam">
                </div>
              </div>

              <h3>Histórico Político</h3>
              <div class="form-grid">
                <div class="form-group">
                  <label for="partyAffiliation">Partido Atual</label>
                  <input type="text" id="partyAffiliation" name="partyAffiliation">
                </div>

                <div class="form-group">
                  <label for="politicalExperience">Anos de Experiência Política</label>
                  <input type="number" id="politicalExperience" name="politicalExperience">
                </div>

                <div class="form-group full-width">
                  <label for="previousPositions">Cargos Anteriores</label>
                  <textarea id="previousPositions" name="previousPositions" rows="3"></textarea>
                </div>

                <div class="form-group full-width">
                  <label for="mainAchievements">Principais Realizações</label>
                  <textarea id="mainAchievements" name="mainAchievements" rows="3"></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Other Tabs (Placeholder) -->
          <div id="comunicacao" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Comunicação e Imagem Pública</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Voz e Tom de Comunicação</li>
                <li>Estilo e Linguagem</li>
                <li>Presença Digital</li>
                <li>Arquétipo de Marca</li>
              </ul>
            </div>
          </div>

          <div id="trajetoria" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Trajetória Política e Legado</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Mandatos e Cargos Executivos</li>
                <li>Projetos de Lei</li>
                <li>Emendas Parlamentares</li>
                <li>Obras e Realizações</li>
              </ul>
            </div>
          </div>

          <div id="plataforma" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Plataforma e Posicionamentos</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Eixos Programáticos</li>
                <li>Posicionamento por Tema</li>
                <li>Propostas Principais</li>
                <li>Bandeiras da Campanha</li>
              </ul>
            </div>
          </div>

          <div id="eleitorado" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Dados Eleitorais e Opinião Pública</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Histórico de Eleições</li>
                <li>Perfil do Eleitorado</li>
                <li>Pesquisas de Opinião</li>
                <li>Geografia do Voto</li>
              </ul>
            </div>
          </div>

          <div id="cenario" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Análise do Ecossistema Político</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Análise SWOT</li>
                <li>Apoio Municipal</li>
                <li>Concorrentes Externos</li>
                <li>Concorrentes Internos</li>
                <li>Monitoramento de Mídia</li>
              </ul>
            </div>
          </div>

          <div id="vulnerabilidades" class="tab-content hidden">
            <div class="placeholder-content">
              <h3>Vulnerabilidades e Pontos de Crise</h3>
              <p>Esta seção será implementada com formulários específicos para:</p>
              <ul>
                <li>Dossiê de Vulnerabilidades</li>
                <li>Declarações Polêmicas</li>
                <li>Plano de Resposta a Crises</li>
                <li>Banco de Argumentos</li>
              </ul>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" onclick="saveData(event)">
              Salvar Dados
            </button>
            <button type="button" class="btn btn-secondary" onclick="previewData()">
              Visualizar Dados
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      function showTab(tabId, element) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => content.classList.add('hidden'));

        // Remove active class from all tabs
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => tab.classList.remove('active'));

        // Show selected tab content
        document.getElementById(tabId).classList.remove('hidden');

        // Add active class to clicked tab
        element.classList.add('active');

        // Prevent default link behavior
        event.preventDefault();
        return false;
      }

      function saveData(event) {
        event.preventDefault();

        // Collect form data
        const formData = new FormData(event.target.form);
        const data = {};

        for (let [key, value] of formData.entries()) {
          data[key] = value;
        }

        console.log('Dados do formulário:', data);
        alert('Dados salvos! Verifique o console do navegador para ver os dados coletados.');
      }

      function previewData() {
        const form = document.querySelector('form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
          if (value.trim()) {
            data[key] = value;
          }
        }

        let preview = 'Dados Preenchidos:\n\n';
        for (let [key, value] of Object.entries(data)) {
          preview += `${key}: ${value}\n`;
        }

        if (Object.keys(data).length === 0) {
          preview = 'Nenhum dado foi preenchido ainda.';
        }

        alert(preview);
      }

      // Auto-save functionality (optional)
      function autoSave() {
        const form = document.querySelector('form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
          data[key] = value;
        }

        localStorage.setItem('candidateFormData', JSON.stringify(data));
      }

      // Load saved data on page load
      function loadSavedData() {
        const savedData = localStorage.getItem('candidateFormData');
        if (savedData) {
          const data = JSON.parse(savedData);
          for (let [key, value] of Object.entries(data)) {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
              element.value = value;
            }
          }
        }
      }

      // Initialize
      document.addEventListener('DOMContentLoaded', function() {
        loadSavedData();

        // Add auto-save on input change
        const form = document.querySelector('form');
        form.addEventListener('input', autoSave);
      });
    </script>
  </body>
</html>
