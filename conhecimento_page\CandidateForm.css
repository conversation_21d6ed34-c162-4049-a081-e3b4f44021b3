.candidate-form {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.8rem;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.form-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  overflow-x: auto;
}

.tab-button {
  flex: 1;
  min-width: 120px;
  padding: 15px 10px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.tab-button:hover {
  background: #e9ecef;
}

.tab-button.active {
  background: #667eea;
  color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-label {
  font-weight: 500;
}

.form-content {
  padding: 30px;
}

.form-section h3 {
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
  font-size: 1.3rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.placeholder-content {
  text-align: center;
  padding: 40px;
  color: #666;
}

.placeholder-content h3 {
  color: #333;
  margin-bottom: 20px;
}

.placeholder-content ul {
  text-align: left;
  max-width: 400px;
  margin: 20px auto;
}

.placeholder-content li {
  margin-bottom: 8px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.submit-button,
.preview-button {
  padding: 12px 30px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button {
  background: #667eea;
  color: white;
}

.submit-button:hover {
  background: #5a6fd8;
}

.preview-button {
  background: #6c757d;
  color: white;
}

.preview-button:hover {
  background: #5a6268;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    min-width: 100px;
    font-size: 0.8rem;
  }

  .form-actions {
    flex-direction: column;
  }
}

