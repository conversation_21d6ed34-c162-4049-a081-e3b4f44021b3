'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FaInstagram, FaHeart, FaComment, FaEye, FaUsers } from 'react-icons/fa';
import { BiTrendingUp, BiTrendingDown } from 'react-icons/bi';

interface InstagramProfile {
    id: string;
    username: string;
    account_type: string;
    media_count: number;
}

interface MediaPost {
    id: string;
    media_type: string;
    media_url: string;
    thumbnail_url?: string;
    caption?: string;
    timestamp: string;
    permalink: string;
}

interface MediaInsights {
    impressions: number;
    reach: number;
    engagement: number;
    likes: number;
    comments: number;
    saves: number;
    shares: number;
}

export default function RedesSociaisPage() {
    const { data: session } = useSession();
    const [profile, setProfile] = useState<InstagramProfile | null>(null);
    const [media, setMedia] = useState<MediaPost[]>([]);
    const [insights, setInsights] = useState<Record<string, MediaInsights>>({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (session?.user?.id) {
            loadInstagramData();
        }
    }, [session]);

    const loadInstagramData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Check if user has Instagram token
            const tokenResponse = await fetch('/api/instagram/token/check', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId: session?.user?.id })
            });

            if (!tokenResponse.ok) {
                throw new Error('Instagram não conectado');
            }

            // Get user profile
            const profileResponse = await fetch('/api/instagram/profile', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId: session?.user?.id })
            });

            if (profileResponse.ok) {
                const profileData = await profileResponse.json();
                setProfile(profileData.profile);
            }

            // Get user media
            const mediaResponse = await fetch('/api/instagram/media', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId: session?.user?.id, limit: 12 })
            });

            if (mediaResponse.ok) {
                const mediaData = await mediaResponse.json();
                setMedia(mediaData.media || []);

                // Get insights for each media post
                const insightsPromises = mediaData.media?.slice(0, 6).map(async (post: MediaPost) => {
                    try {
                        const insightResponse = await fetch(`/api/instagram/insights/${post.id}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ userId: session?.user?.id })
                        });
                        
                        if (insightResponse.ok) {
                            const insightData = await insightResponse.json();
                            return { [post.id]: insightData.insights };
                        }
                    } catch (err) {
                        console.error('Error loading insights for post:', post.id, err);
                    }
                    return null;
                });

                const insightsResults = await Promise.all(insightsPromises);
                const insightsMap = insightsResults.reduce((acc, result) => {
                    if (result) Object.assign(acc, result);
                    return acc;
                }, {});
                
                setInsights(insightsMap);
            }

        } catch (err) {
            setError(err instanceof Error ? err.message : 'Erro ao carregar dados do Instagram');
        } finally {
            setLoading(false);
        }
    };

    const calculateAverageEngagement = () => {
        const validInsights = Object.values(insights).filter(insight => insight.engagement);
        if (validInsights.length === 0) return 0;
        
        const total = validInsights.reduce((sum, insight) => sum + insight.engagement, 0);
        return Math.round(total / validInsights.length);
    };

    const calculateAverageLikes = () => {
        const validInsights = Object.values(insights).filter(insight => insight.likes);
        if (validInsights.length === 0) return 0;
        
        const total = validInsights.reduce((sum, insight) => sum + insight.likes, 0);
        return Math.round(total / validInsights.length);
    };

    if (loading) {
        return (
            <div className="container mx-auto p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <p className="text-gray-400">Carregando dados do Instagram...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto p-6">
                <Card className="bg-red-900/20 border-red-500">
                    <CardContent className="p-6 text-center">
                        <FaInstagram className="mx-auto mb-4 text-4xl text-red-400" />
                        <h3 className="text-lg font-semibold text-white mb-2">Instagram não conectado</h3>
                        <p className="text-gray-400 mb-4">{error}</p>
                        <Button 
                            onClick={() => window.location.href = '/panel/instagram-test'}
                            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                        >
                            <FaInstagram className="mr-2" />
                            Conectar Instagram
                        </Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-white">Redes Sociais</h1>
                    <p className="text-gray-400">Monitore o desempenho das suas redes sociais</p>
                </div>
                <Button 
                    onClick={loadInstagramData}
                    variant="outline"
                    className="border-blue-500 text-blue-400 hover:bg-blue-500/10"
                >
                    Atualizar Dados
                </Button>
            </div>

            {/* Instagram Profile Info */}
            {profile && (
                <Card className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 border-purple-500/30">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-3">
                            <FaInstagram className="text-2xl text-pink-400" />
                            <div>
                                <span className="text-white">@{profile.username}</span>
                                <Badge variant="secondary" className="ml-2 bg-purple-500/20 text-purple-300">
                                    {profile.account_type}
                                </Badge>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-white">{profile.media_count}</div>
                                <div className="text-sm text-gray-400">Posts</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-white">{calculateAverageLikes()}</div>
                                <div className="text-sm text-gray-400">Média de Curtidas</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-white">{calculateAverageEngagement()}</div>
                                <div className="text-sm text-gray-400">Engajamento Médio</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-white">{Object.keys(insights).length}</div>
                                <div className="text-sm text-gray-400">Posts Analisados</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Recent Posts */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-white">Posts Recentes</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {media.slice(0, 6).map((post) => {
                            const postInsights = insights[post.id];
                            return (
                                <div key={post.id} className="bg-gray-800/50 rounded-lg p-4 space-y-3">
                                    {/* Media Preview */}
                                    <div className="aspect-square bg-gray-700 rounded-lg overflow-hidden">
                                        {post.media_type === 'IMAGE' && post.media_url && (
                                            <img 
                                                src={post.media_url} 
                                                alt="Instagram post"
                                                className="w-full h-full object-cover"
                                            />
                                        )}
                                        {post.media_type === 'VIDEO' && post.thumbnail_url && (
                                            <img 
                                                src={post.thumbnail_url} 
                                                alt="Video thumbnail"
                                                className="w-full h-full object-cover"
                                            />
                                        )}
                                    </div>

                                    {/* Post Info */}
                                    <div className="space-y-2">
                                        <div className="text-xs text-gray-400">
                                            {new Date(post.timestamp).toLocaleDateString('pt-BR')}
                                        </div>
                                        
                                        {post.caption && (
                                            <p className="text-sm text-gray-300 line-clamp-2">
                                                {post.caption.substring(0, 100)}...
                                            </p>
                                        )}

                                        {/* Insights */}
                                        {postInsights && (
                                            <div className="grid grid-cols-2 gap-2 text-xs">
                                                <div className="flex items-center gap-1 text-red-400">
                                                    <FaHeart className="text-xs" />
                                                    {postInsights.likes || 0}
                                                </div>
                                                <div className="flex items-center gap-1 text-blue-400">
                                                    <FaComment className="text-xs" />
                                                    {postInsights.comments || 0}
                                                </div>
                                                <div className="flex items-center gap-1 text-green-400">
                                                    <FaEye className="text-xs" />
                                                    {postInsights.reach || 0}
                                                </div>
                                                <div className="flex items-center gap-1 text-purple-400">
                                                    <BiTrendingUp className="text-xs" />
                                                    {postInsights.engagement || 0}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
