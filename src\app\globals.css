@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        /* Light theme - keep as fallback */
        --background: 214 24% 96%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 191 100% 32%;
        --primary-foreground: 0 0% 98%;
        --secondary: 16, 79%, 60%;
        --secondary-foreground: 0 0% 9%;
        --muted: 220 20% 82%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    /* Make dark theme the default */
    :root {
        /* Dark Theme Base Colors */
        --background: 220 25% 10%;
        --foreground: 210 20% 98%;
        --card: 220 30% 13%;
        --card-foreground: 210 20% 98%;
        --popover: 220 30% 13%;
        --popover-foreground: 210 20% 98%;

        /* Primary & Secondary Colors - Based on Kairos Brand */
        --primary: 191 100% 50%;
        --primary-foreground: 210 20% 98%;
        --secondary: 16 79% 60%;
        --secondary-foreground: 210 40% 98%;

        /* UI Element Colors */
        --muted: 220 15% 20%;
        --muted-foreground: 220 10% 70%;
        --accent: 191 70% 20%;
        --accent-foreground: 210 20% 98%;
        --destructive: 0 70% 50%;
        --destructive-foreground: 210 20% 98%;

        /* Border & Input Colors */
        --border: 220 15% 20%;
        --input: 220 15% 20%;
        --ring: 191 100% 50%;

        /* Chart Colors - More vibrant on dark backgrounds */
        --chart-1: 191 100% 50%;
        --chart-2: 220 70% 60%;
        --chart-3: 16 79% 60%;
        --chart-4: 150 70% 50%;
        --chart-5: 270 70% 60%;

        /* Sidebar - Dark Theme */
        --sidebar-background: 220 30% 13%;
        --sidebar-foreground: 210 20% 98%;
        --sidebar-primary: 191 100% 50%;
        --sidebar-primary-foreground: 220 30% 13%;
        --sidebar-accent: 220 20% 16%;
        --sidebar-accent-foreground: 210 20% 98%;
        --sidebar-border: 220 20% 16%;
        --sidebar-ring: 191 100% 50%;
    }

    /* Keep light theme class for potential future toggle */
    .light {
        --background: 214 24% 96%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 191 100% 32%;
        --primary-foreground: 0 0% 98%;
        --secondary: 16 79% 60%;
        --secondary-foreground: 0 0% 9%;
        --muted: 220 20% 82%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

.fc-today-button {
    display: none !important;
}
.fc-toolbar-title {
    text-transform: capitalize;
}

.fc-prev-button,
.fc-next-button {
    margin: 0px 4px !important;
    border-radius: 50% !important;
    padding: 8px !important;
    display: grid !important;
    align-items: center !important;
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    transition: all 0.3s;
}

.fc-prev-button:hover,
.fc-next-button:hover {
    transform: scale(1.1);
    background-color: hsl(var(--primary) / 0.85) !important;
}
.fc-daygrid-event-harness {
    cursor: pointer;
}

/* Updated Calendar Styles for Dark Theme */
table.fc-scrollgrid.fc-scrollgrid-liquid {
    border-color: hsl(var(--border));
    overflow: hidden;
    border-radius: 4px;
    background-color: hsl(var(--card));
}

table.fc-scrollgrid.fc-scrollgrid-liquid * {
    border-color: hsl(var(--border)) !important;
}

textarea {
    width: 100%;
    max-width: 100%;
    field-sizing: content;
}

/* Calendar Custom Styles - Updated for Dark Theme */
.fc {
    /* Calendar Container */
    --fc-border-color: hsl(var(--border));
    --fc-today-bg-color: hsl(var(--primary) / 0.2);
    --fc-neutral-bg-color: hsl(var(--background));
    --fc-list-event-hover-bg-color: hsl(var(--primary) / 0.1);
    --fc-page-bg-color: hsl(var(--card));
    color: hsl(var(--foreground));
}

.fc .fc-toolbar-title {
    @apply text-lg font-semibold text-foreground;
}

.fc .fc-button {
    @apply !bg-primary !text-white hover:!bg-primary/90;
}

.fc .fc-button .fc-icon {
    @apply !text-white;
}

.fc .fc-prev-button,
.fc .fc-next-button {
    @apply !border-0 !shadow-none;
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
    @apply bg-accent text-accent-foreground;
}

.fc .fc-daygrid-day-number {
    @apply p-2 text-sm font-medium text-foreground/80;
}

.fc .fc-daygrid-event {
    @apply px-2 py-1 text-xs font-medium;
}

.fc .fc-col-header-cell {
    @apply border-border;
}

.fc .fc-scrollgrid {
    @apply border-border;
}

/* Calendar Container */
.fc {
    @apply overflow-hidden rounded-lg border border-border;
}

/* Header Styles */
.fc-header-toolbar {
    @apply mb-0 border-b border-border px-4 py-4 !important;
}

/* Day Header */
.fc-col-header {
    @apply bg-muted/30;
}

/* Events */
.fc-event {
    @apply cursor-pointer border-0 bg-primary text-primary-foreground shadow-sm transition-opacity hover:opacity-90;
}

/* Today */
.fc-day-today {
    @apply bg-primary/10 !important;
}

/* Hover Effects */
.fc-daygrid-day:hover {
    @apply bg-muted/50 transition-colors;
}

/* Cards with glow effect */
.card-glow {
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: all 0.4s ease;
}

.card-glow::before {
    content: '';
    position: absolute;
    z-index: -1;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: linear-gradient(
        135deg,
        transparent 0%,
        transparent 35%,
        rgba(92, 225, 230, 0.05) 40%,
        rgba(92, 225, 230, 0.1) 45%,
        rgba(0, 133, 163, 0.3) 50%,
        rgba(92, 225, 230, 0.1) 55%,
        rgba(92, 225, 230, 0.05) 60%,
        transparent 65%,
        transparent 100%
    );
    animation: diagonal-sweep 2s linear infinite;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.card-glow:hover::before,
.card-glow.selected::before {
    opacity: 1;
}

@keyframes diagonal-sweep {
    0% {
        transform: translateX(-33.33%) translateY(-33.33%);
    }
    100% {
        transform: translateX(33.33%) translateY(33.33%);
    }
}

/* Animation for the arrow pointing down */
.rotate-180 {
    transform: rotate(180deg);
}

/* Animation for the bounce effect */
@keyframes bounce {
    0%,
    100% {
        transform: translateY(0) rotate(180deg);
    }
    50% {
        transform: translateY(-25%) rotate(180deg);
    }
}

.animate-bounce-arrow {
    animation: bounce 1s ease infinite;
}

/* Custom button gradient for all buttons */
.btn-primary,
.bg-primary,
button[class*='bg-primary'],
a[class*='bg-primary'],
.fc .fc-button,
.fc-event,
[class*='btn-primary'] {
    background: linear-gradient(to right, hsl(191, 65%, 53%), #0085a3) !important;
    border-color: #0085a3 !important;
    color: hsl(var(--card)) !important;
}

/* Hover effect for gradient buttons */
.btn-primary:hover,
.bg-primary:hover,
button[class*='bg-primary']:hover,
a[class*='bg-primary']:hover,
.fc .fc-button:hover {
    background: linear-gradient(to right, hsl(191, 75%, 58%), hsl(191, 100%, 38%)) !important;
    box-shadow: 0 0 15px rgba(0, 133, 163, 0.3);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.5);
}

/* Ajustes para os componentes Tabs no Dark Mode */
.tabs-dark-mode [data-state='inactive'] {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.tabs-dark-mode [data-state='inactive']:hover {
    opacity: 0.9;
}

.tabs-dark-mode [data-state='active'] {
    position: relative;
    z-index: 1;
    font-weight: 500;
}

/* Botão de criar vídeo no modo escuro */
.create-video-button {
    background: linear-gradient(to right, hsl(191, 65%, 53%), #0085a3);
    color: white;
    transition: all 0.2s ease;
}

.create-video-button:hover {
    filter: brightness(1.1);
    box-shadow: 0 0 15px rgba(0, 133, 163, 0.3);
    transform: translateY(-1px);
}

/* Inputs no modo escuro */
.dark-input {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
}

.dark-input::placeholder {
    color: hsl(var(--muted-foreground));
}

.dark-input:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* Social Media Icons Styling */
.social-icon-instagram {
    /* Keep Instagram's colored logo - no filter */
    filter: none;
}

.social-icon-tiktok {
    /* Keep TikTok's white logo - no filter */
    filter: none;
}

.social-icon-x {
    /* Make X logo white */
    filter: brightness(0) invert(1);
}

/* Alternative styling for better contrast */
.social-icon-overlay {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

/* Instagram specific overlay - lighter background for colored logo */
.social-icon-overlay.instagram {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

/* TikTok specific overlay - keep black background for white logo */
.social-icon-overlay.tiktok {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(4px);
}
